# Accounts Module - Transactions Menu Flowcharts

This directory contains comprehensive Mermaid flowcharts for all major transaction types in the Accounts module of the ERP system. Each flowchart provides detailed business logic, workflow processes, validation rules, and integration points for both ASP.NET and Django implementations.

## 📋 Completed Flowcharts

### 1. Proforma Invoice (`proforma_invoice_flowchart.mmd`)
**Comprehensive business flow for Proforma Invoice functionality**
- **ASP.NET Pages**: ProformaInvoice_New.aspx, ProformaInvoice_Edit.aspx, ProformaInvoice_Delete.aspx, ProformaInvoice_Print.aspx
- **Django Views**: ProformaInvoiceListView, ProformaInvoiceCreateView, ProformaInvoiceUpdateView, ProformaInvoiceDeleteView
- **Key Features**:
  - Complete validation and business rules
  - Tax calculations (VAT, CST, Excise, Service Tax)
  - Workflow management (Draft → Validation → Calculation → Review → Approval → Send → Track → Convert/Expire)
  - Integration with Sales, Inventory, Purchase, and Accounts modules
  - Crystal Reports integration for printing

### 2. Sales Invoice (`sales_invoice_flowchart.mmd`)
**Detailed business flow for Sales Invoice functionality**
- **ASP.NET Pages**: SalesInvoice_Dashboard.aspx, SalesInvoice_New.aspx, SalesInvoice_Edit.aspx, SalesInvoice_Delete.aspx, SalesInvoice_Print.aspx
- **Django Views**: SalesInvoiceListView, SalesInvoiceCreateView, SalesInvoiceUpdateView, SalesInvoiceDeleteView
- **Key Features**:
  - Payment tracking and management
  - Credit limit validation
  - Multiple payment methods (Cash, Bank, Credit Card, Online)
  - Advanced workflow (Draft → Validation → Credit Check → Approval → Dispatch → Payment → Closure)
  - Payment reconciliation and outstanding tracking

### 3. Service Tax Invoice (`service_tax_invoice_flowchart.mmd`)
**Comprehensive flow for Service Tax Invoice with compliance features**
- **ASP.NET Pages**: ServiceTaxInvoice_Dashboard.aspx, ServiceTaxInvoice_New.aspx, ServiceTaxInvoice_Edit.aspx, ServiceTaxInvoice_Print.aspx
- **Django Views**: ServiceTaxInvoiceListView, ServiceTaxInvoiceCreateView, ServiceTaxInvoiceUpdateView
- **Key Features**:
  - Service tax compliance and regulatory validation
  - Reverse charge mechanism
  - Abatement calculations
  - Education cess and secondary education cess
  - Place of service validation
  - Service tax return integration

### 4. Voucher Management (`voucher_management_flowchart.mmd`)
**Detailed flow for Cash Voucher, Bank Voucher, and Contra Entry processes**
- **ASP.NET Pages**: CashVoucher_New.aspx, BankVoucher.aspx, ContraEntry.aspx
- **Django Views**: CashVoucherListView, BankVoucherListView, ContraEntryListView
- **Key Features**:
  - Multi-voucher type management
  - TDS calculations and validations
  - Cheque management and clearance tracking
  - Bank reconciliation integration
  - Payment method validation (NEFT, RTGS, Cheque)
  - Comprehensive audit trail

### 5. Bill Management (`bill_management_flowchart.mmd`)
**Comprehensive flow for Bill Booking with authorization workflows**
- **ASP.NET Pages**: BillBooking_New.aspx, BillBooking_Edit.aspx, BillBooking_Authorize.aspx, BillBooking_ItemGrid.aspx
- **Django Views**: BillBookingListView, BillBookingCreateView, BillBookingAuthorizeView
- **Key Features**:
  - Multi-level authorization workflow
  - Purchase order and GRR integration
  - Item grid management
  - Tax calculations and validations
  - Supplier integration
  - Authorization levels (Department Head → Finance Manager → General Manager)

### 6. Credit/Debit Notes (`credit_debit_notes_flowchart.mmd`)
**Detailed flow for Credit Note and Debit Note processes**
- **ASP.NET Pages**: Credit_Note.aspx, Debit_Note.aspx
- **Django Views**: CreditNoteListView, DebitNoteListView, CreditNoteCreateView, DebitNoteCreateView
- **Key Features**:
  - Account adjustment processing
  - Reference validation (Invoice/Bill integration)
  - Tax adjustment calculations
  - Customer/Supplier balance updates
  - Multiple note types (Sales Return, Purchase Return, Account Adjustment, Error Correction)
  - Comprehensive ledger integration

### 7. Financial Transactions (`financial_transactions_flowchart.mmd`)
**Comprehensive flow for Asset Register, Capital Management, and Loan processes**
- **ASP.NET Pages**: Asset_Register.aspx, Capital.aspx, ACC_LoanMaster.aspx, CurrentLiabilities.aspx
- **Django Views**: AssetRegisterListView, CapitalListView, LoanMasterListView, CurrentAssetsListView
- **Key Features**:
  - Asset management with depreciation calculations
  - Capital structure analysis and management
  - Loan management with EMI calculations
  - Financial ratio calculations
  - Asset performance analysis
  - Capital adequacy monitoring
  - Loan portfolio management

### 8. Sundry Management (`sundry_management_flowchart.mmd`)
**Detailed flow for Sundry Creditors and Debitors management**
- **ASP.NET Pages**: SundryCreditors.aspx, SundryCreditors_Details.aspx, Acc_Sundry_CustList.aspx, CreditorsDebitors.aspx
- **Django Views**: SundryCreditorListView, SundryCustomerListView, SundryCreditorCreateView, SundryCustomerCreateView
- **Key Features**:
  - Comprehensive creditors and debitors management
  - Outstanding balance tracking and aging analysis
  - Credit limit management and validation
  - Account reconciliation and settlement
  - Collection workflow and follow-up processes
  - Financial risk assessment and reporting

### 9. Other Transactions (`other_transactions_flowchart.mmd`)
**Comprehensive flow for IOU, Advice, Tour Voucher, and Bank Reconciliation processes**
- **ASP.NET Pages**: IOU_PaymentReceipt.aspx, Advice.aspx, TourVoucher.aspx, BankReconciliation_New.aspx
- **Django Views**: IOUListView, AdviceListView, TourVoucherListView, BankReconciliationListView
- **Key Features**:
  - IOU management with authorization workflows
  - Advice payment and receipt processing
  - Tour voucher and expense management
  - Bank reconciliation with automatic matching
  - Employee expense tracking and settlement
  - Integration with HR and payroll modules

### 10. Reporting & Communication (`reporting_communication_flowchart.mmd`)
**Comprehensive flow for Mail Merge and Balance Sheet functionality**
- **ASP.NET Pages**: MailMerge.aspx, BalanceSheet.aspx, Acc_Capital_Particulars.aspx
- **Django Views**: MailMergeTemplateListView, BalanceSheetView, MailMergeCreateView, BalanceSheetConfigView
- **Key Features**:
  - Mail merge template management and execution
  - Balance sheet generation and analysis
  - Financial reporting and compliance
  - Email communication and document generation
  - Template-based document creation
  - Financial ratio analysis and trend reporting

## 🎨 Flowchart Color Coding

Each flowchart uses a consistent color scheme for easy identification:

- **🔵 ASP.NET Pages** (Light Blue): `#e1f5fe` - ASP.NET web forms and pages
- **🟢 Django Views** (Light Green): `#e8f5e8` - Django class-based views
- **🟠 Database Tables** (Light Orange): `#fff3e0` - Database tables and relationships
- **🟣 Business Logic** (Light Purple): `#f3e5f5` - Business logic components
- **🔴 Validation** (Light Red): `#ffebee` - Validation rules and checks
- **🟢 Workflow** (Light Teal): `#e0f2f1` - Workflow processes and status
- **🟡 Integration** (Light Pink): `#fce4ec` - Integration points and APIs
- **🔵 Calculation** (Light Blue): `#e3f2fd` - Calculation engines and formulas
- **🟢 Financial** (Light Green): `#e8f5e8` - Financial analysis and reporting

## 🔗 Cross-System Parity

All flowcharts demonstrate functional parity between ASP.NET and Django systems:
- Identical business logic implementation
- Consistent validation rules
- Equivalent workflow processes
- Same database schema utilization
- Parallel integration points

## 📊 Business Logic Coverage

Each flowchart includes:
1. **Complete CRUD Operations** - Create, Read, Update, Delete functionality
2. **Validation Layers** - Field validation, business rules, and compliance checks
3. **Calculation Engines** - Tax calculations, financial computations, and adjustments
4. **Workflow Management** - Status transitions and approval processes
5. **Integration Points** - Module interconnections and external system integrations
6. **Reporting Capabilities** - Crystal Reports and financial reporting integration

## 🚀 Usage Instructions

1. **View Flowcharts**: Use any Mermaid-compatible viewer or renderer
2. **Edit Flowcharts**: Modify `.mmd` files using any text editor
3. **Render Diagrams**: Use Mermaid CLI, online editors, or integrated development environments
4. **Integration**: Reference these flowcharts for system development and maintenance

## 📝 Notes

- All flowcharts maintain strict database schema immutability (`managed=False`)
- Business logic ensures complete functional parity between systems
- Validation rules follow established ERP best practices
- Integration patterns support scalable system architecture
- Workflow processes align with standard accounting practices

## ✅ Complete Transaction Coverage

All major Transactions menu items have been comprehensively covered:

### Completed Transaction Types:
1. **Invoice Management** - Proforma Invoice, Sales Invoice, Service Tax Invoice
2. **Voucher Management** - Cash Voucher, Bank Voucher, Contra Entry
3. **Bill Management** - Bill Booking with authorization workflows
4. **Adjustment Management** - Credit Notes, Debit Notes
5. **Financial Management** - Asset Register, Capital Management, Loan Master
6. **Sundry Management** - Creditors and Debitors management
7. **Other Transactions** - IOU, Advice, Tour Voucher, Bank Reconciliation
8. **Reporting & Communication** - Mail Merge, Balance Sheet

### Transaction Menu Items Mapped:
- ✅ Proforma Invoice
- ✅ Sales Invoice
- ✅ Services Invoice
- ✅ Contra
- ✅ Debit Note
- ✅ Credit Note
- ✅ IOU Payment/Receipt
- ✅ Bill Booking
- ✅ Authorize Bill Booking
- ✅ Cash Voucher
- ✅ Payment/Receipt Voucher
- ✅ Advice
- ✅ Tour Voucher
- ✅ Creditors/Debitors
- ✅ Bank Reconciliation
- ✅ Mail Merge
- ✅ Balance Sheet
- ✅ Asset Register
- ✅ Loan Master
- ✅ Capital Master

## 🔄 Future Enhancements

Potential areas for expansion:
- Masters Menu flowcharts (Account Setup, Tax Setup, Financial Setup)
- Reports Menu flowcharts (Financial Reports, Crystal Reports)
- Advanced analytics and dashboard integrations
- Mobile application workflow integration

---

*Generated as part of comprehensive ERP system documentation and analysis.*
