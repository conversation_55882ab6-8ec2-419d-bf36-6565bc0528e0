graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Material Costing Module Structure
    subgraph MC_MODULE["💰 Material Costing Module - Cost Management & Analysis System"]
        direction TB
        
        %% Entry Points
        MC_DASHBOARD[("📊 Material Costing Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Material Costing Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Material Management
                subgraph MATERIAL_MGMT["📦 Material Management"]
                    MATERIAL_NEW["Material_New.aspx<br/>Create Material"]:::aspnetPage
                    MATERIAL_EDIT["Material_Edit.aspx<br/>Edit Material"]:::aspnetPage
                    MATERIAL_DELETE["Material_Delete.aspx<br/>Delete Material"]:::aspnetPage
                    MATERIAL_CATEGORY["Material_Category.aspx<br/>Material Category"]:::aspnetPage
                end
                
                %% Cost Centers
                subgraph COST_CENTERS["🏢 Cost Centers"]
                    COST_CENTER_MASTER["CostCenter_Master.aspx<br/>Cost Center Master"]:::aspnetPage
                    COST_CENTER_HIERARCHY["CostCenter_Hierarchy.aspx<br/>Cost Center Hierarchy"]:::aspnetPage
                    COST_CENTER_ALLOCATION["CostCenter_Allocation.aspx<br/>Cost Center Allocation"]:::aspnetPage
                    OVERHEAD_ALLOCATION["Overhead_Allocation.aspx<br/>Overhead Allocation"]:::aspnetPage
                end
                
                %% Costing Methods
                subgraph COSTING_METHODS["⚙️ Costing Methods"]
                    COSTING_METHOD_MASTER["CostingMethod_Master.aspx<br/>Costing Method Master"]:::aspnetPage
                    STANDARD_COSTING["Standard_Costing.aspx<br/>Standard Costing"]:::aspnetPage
                    ACTUAL_COSTING["Actual_Costing.aspx<br/>Actual Costing"]:::aspnetPage
                    AVERAGE_COSTING["Average_Costing.aspx<br/>Average Costing"]:::aspnetPage
                    FIFO_COSTING["FIFO_Costing.aspx<br/>FIFO Costing"]:::aspnetPage
                    LIFO_COSTING["LIFO_Costing.aspx<br/>LIFO Costing"]:::aspnetPage
                end
                
                %% Cost Elements
                subgraph COST_ELEMENTS["💸 Cost Elements"]
                    MATERIAL_COST["Material_Cost.aspx<br/>Material Cost"]:::aspnetPage
                    LABOR_COST["Labor_Cost.aspx<br/>Labor Cost"]:::aspnetPage
                    OVERHEAD_COST["Overhead_Cost.aspx<br/>Overhead Cost"]:::aspnetPage
                    INDIRECT_COST["Indirect_Cost.aspx<br/>Indirect Cost"]:::aspnetPage
                    FREIGHT_COST["Freight_Cost.aspx<br/>Freight Cost"]:::aspnetPage
                    HANDLING_COST["Handling_Cost.aspx<br/>Handling Cost"]:::aspnetPage
                end
                
                %% Rate Masters
                subgraph RATE_MASTERS["📊 Rate Masters"]
                    LABOR_RATE_MASTER["LaborRate_Master.aspx<br/>Labor Rate Master"]:::aspnetPage
                    MACHINE_RATE_MASTER["MachineRate_Master.aspx<br/>Machine Rate Master"]:::aspnetPage
                    OVERHEAD_RATE_MASTER["OverheadRate_Master.aspx<br/>Overhead Rate Master"]:::aspnetPage
                    CURRENCY_RATE_MASTER["CurrencyRate_Master.aspx<br/>Currency Rate Master"]:::aspnetPage
                    EXCHANGE_RATE_MASTER["ExchangeRate_Master.aspx<br/>Exchange Rate Master"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: MaterialCosting_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Cost Calculation
                subgraph COST_CALCULATION["🧮 Cost Calculation"]
                    MATERIAL_COST_CALC["MaterialCost_Calculation.aspx<br/>Material Cost Calculation"]:::aspnetPage
                    BOM_COSTING["BOM_Costing.aspx<br/>BOM Costing"]:::aspnetPage
                    PRODUCT_COSTING["Product_Costing.aspx<br/>Product Costing"]:::aspnetPage
                    ASSEMBLY_COSTING["Assembly_Costing.aspx<br/>Assembly Costing"]:::aspnetPage
                    SUBASSEMBLY_COSTING["SubAssembly_Costing.aspx<br/>Sub-Assembly Costing"]:::aspnetPage
                    COMPONENT_COSTING["Component_Costing.aspx<br/>Component Costing"]:::aspnetPage
                end
                
                %% Standard Cost Management
                subgraph STANDARD_COST_MGMT["📏 Standard Cost Management"]
                    STANDARD_COST_SETUP["StandardCost_Setup.aspx<br/>Standard Cost Setup"]:::aspnetPage
                    STANDARD_COST_UPDATE["StandardCost_Update.aspx<br/>Standard Cost Update"]:::aspnetPage
                    STANDARD_COST_REVISION["StandardCost_Revision.aspx<br/>Standard Cost Revision"]:::aspnetPage
                    STANDARD_COST_ROLLUP["StandardCost_Rollup.aspx<br/>Standard Cost Rollup"]:::aspnetPage
                    STANDARD_COST_FREEZE["StandardCost_Freeze.aspx<br/>Standard Cost Freeze"]:::aspnetPage
                end
                
                %% Actual Cost Processing
                subgraph ACTUAL_COST_PROC["📊 Actual Cost Processing"]
                    ACTUAL_COST_CAPTURE["ActualCost_Capture.aspx<br/>Actual Cost Capture"]:::aspnetPage
                    ACTUAL_COST_ALLOCATION["ActualCost_Allocation.aspx<br/>Actual Cost Allocation"]:::aspnetPage
                    ACTUAL_COST_ABSORPTION["ActualCost_Absorption.aspx<br/>Actual Cost Absorption"]:::aspnetPage
                    ACTUAL_COST_POSTING["ActualCost_Posting.aspx<br/>Actual Cost Posting"]:::aspnetPage
                    ACTUAL_COST_ADJUSTMENT["ActualCost_Adjustment.aspx<br/>Actual Cost Adjustment"]:::aspnetPage
                end
                
                %% Variance Analysis
                subgraph VARIANCE_ANALYSIS["📈 Variance Analysis"]
                    MATERIAL_VARIANCE["Material_Variance.aspx<br/>Material Variance"]:::aspnetPage
                    LABOR_VARIANCE["Labor_Variance.aspx<br/>Labor Variance"]:::aspnetPage
                    OVERHEAD_VARIANCE["Overhead_Variance.aspx<br/>Overhead Variance"]:::aspnetPage
                    PRICE_VARIANCE["Price_Variance.aspx<br/>Price Variance"]:::aspnetPage
                    QUANTITY_VARIANCE["Quantity_Variance.aspx<br/>Quantity Variance"]:::aspnetPage
                    EFFICIENCY_VARIANCE["Efficiency_Variance.aspx<br/>Efficiency Variance"]:::aspnetPage
                end
                
                %% Cost Allocation
                subgraph COST_ALLOCATION["🔄 Cost Allocation"]
                    DIRECT_COST_ALLOCATION["DirectCost_Allocation.aspx<br/>Direct Cost Allocation"]:::aspnetPage
                    INDIRECT_COST_ALLOCATION["IndirectCost_Allocation.aspx<br/>Indirect Cost Allocation"]:::aspnetPage
                    OVERHEAD_ALLOCATION_TRANS["Overhead_Allocation.aspx<br/>Overhead Allocation"]:::aspnetPage
                    ACTIVITY_BASED_COSTING["ActivityBased_Costing.aspx<br/>Activity Based Costing"]:::aspnetPage
                    COST_DRIVER_ALLOCATION["CostDriver_Allocation.aspx<br/>Cost Driver Allocation"]:::aspnetPage
                end
                
                %% Cost Rollup
                subgraph COST_ROLLUP["📊 Cost Rollup"]
                    COST_ROLLUP_PROCESS["CostRollup_Process.aspx<br/>Cost Rollup Process"]:::aspnetPage
                    MULTILEVEL_ROLLUP["MultiLevel_Rollup.aspx<br/>Multi-Level Rollup"]:::aspnetPage
                    SINGLE_LEVEL_ROLLUP["SingleLevel_Rollup.aspx<br/>Single Level Rollup"]:::aspnetPage
                    COST_EXPLOSION["Cost_Explosion.aspx<br/>Cost Explosion"]:::aspnetPage
                    COST_IMPLOSION["Cost_Implosion.aspx<br/>Cost Implosion"]:::aspnetPage
                end
                
                %% Inventory Valuation
                subgraph INVENTORY_VALUATION["📦 Inventory Valuation"]
                    INVENTORY_VALUATION_PROCESS["InventoryValuation_Process.aspx<br/>Inventory Valuation Process"]:::aspnetPage
                    STOCK_VALUATION["Stock_Valuation.aspx<br/>Stock Valuation"]:::aspnetPage
                    WIP_VALUATION["WIP_Valuation.aspx<br/>WIP Valuation"]:::aspnetPage
                    FINISHED_GOODS_VALUATION["FinishedGoods_Valuation.aspx<br/>Finished Goods Valuation"]:::aspnetPage
                    REVALUATION_PROCESS["Revaluation_Process.aspx<br/>Revaluation Process"]:::aspnetPage
                end
                
                %% Cost Simulation
                subgraph COST_SIMULATION["🎯 Cost Simulation"]
                    COST_SIMULATION_PROCESS["CostSimulation_Process.aspx<br/>Cost Simulation Process"]:::aspnetPage
                    WHAT_IF_ANALYSIS["WhatIf_Analysis.aspx<br/>What-If Analysis"]:::aspnetPage
                    SCENARIO_PLANNING["Scenario_Planning.aspx<br/>Scenario Planning"]:::aspnetPage
                    COST_OPTIMIZATION["Cost_Optimization.aspx<br/>Cost Optimization"]:::aspnetPage
                    SENSITIVITY_ANALYSIS["Sensitivity_Analysis.aspx<br/>Sensitivity Analysis"]:::aspnetPage
                end
            end
