graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Quality Control Module Structure
    subgraph QC_MODULE["🔍 Quality Control Module - Quality Assurance & Inspection System"]
        direction TB
        
        %% Entry Points
        QC_DASHBOARD[("📊 Quality Control Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Quality Control Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Quality Parameters
                subgraph QUALITY_PARAMS["🔬 Quality Parameters"]
                    QUALITY_STANDARDS["Quality_Standards.aspx<br/>Quality Standards Master"]:::aspnetPage
                    INSPECTION_CRITERIA["Inspection_Criteria.aspx<br/>Inspection Criteria"]:::aspnetPage
                    TEST_METHODS["Test_Methods.aspx<br/>Test Methods"]:::aspnetPage
                    QUALITY_LEVELS["Quality_Levels.aspx<br/>Quality Levels"]:::aspnetPage
                    DEFECT_CODES["Defect_Codes.aspx<br/>Defect Codes"]:::aspnetPage
                    REJECTION_REASONS["Rejection_Reasons.aspx<br/>Rejection Reasons"]:::aspnetPage
                    SAMPLING_PLANS["Sampling_Plans.aspx<br/>Sampling Plans"]:::aspnetPage
                    AQL_STANDARDS["AQL_Standards.aspx<br/>AQL Standards"]:::aspnetPage
                end
                
                %% Inspection Setup
                subgraph INSPECTION_SETUP["🔍 Inspection Setup"]
                    INSPECTION_TYPES["Inspection_Types.aspx<br/>Inspection Types"]:::aspnetPage
                    INSPECTION_STAGES["Inspection_Stages.aspx<br/>Inspection Stages"]:::aspnetPage
                    INSPECTION_POINTS["Inspection_Points.aspx<br/>Inspection Points"]:::aspnetPage
                    QUALITY_CHECKPOINTS["Quality_Checkpoints.aspx<br/>Quality Checkpoints"]:::aspnetPage
                    MEASUREMENT_UNITS["Measurement_Units.aspx<br/>Measurement Units"]:::aspnetPage
                    TOLERANCE_LIMITS["Tolerance_Limits.aspx<br/>Tolerance Limits"]:::aspnetPage
                end
                
                %% Equipment & Instruments
                subgraph EQUIPMENT_SETUP["🔧 Equipment & Instruments"]
                    MEASURING_INSTRUMENTS["Measuring_Instruments.aspx<br/>Measuring Instruments"]:::aspnetPage
                    CALIBRATION_SCHEDULE["Calibration_Schedule.aspx<br/>Calibration Schedule"]:::aspnetPage
                    EQUIPMENT_MASTER["Equipment_Master.aspx<br/>Equipment Master"]:::aspnetPage
                    GAUGE_MASTER["Gauge_Master.aspx<br/>Gauge Master"]:::aspnetPage
                    INSTRUMENT_CATEGORY["Instrument_Category.aspx<br/>Instrument Category"]:::aspnetPage
                end
                
                %% Quality Personnel
                subgraph QUALITY_PERSONNEL["👥 Quality Personnel"]
                    INSPECTOR_MASTER["Inspector_Master.aspx<br/>Inspector Master"]:::aspnetPage
                    QC_TEAM["QC_Team.aspx<br/>QC Team"]:::aspnetPage
                    QUALITY_ROLES["Quality_Roles.aspx<br/>Quality Roles"]:::aspnetPage
                    CERTIFICATION_MASTER["Certification_Master.aspx<br/>Certification Master"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: QualityControl_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Quality Note Management
                subgraph QUALITY_NOTE_MGMT["📝 Quality Note Management"]
                    QUALITY_NOTE_DASH["QualityNote_Dashboard.aspx<br/>Quality Note Dashboard"]:::aspnetPage
                    
                    %% Goods Quality Note (GQN)
                    subgraph GQN_SECTION["📋 Goods Quality Note (GQN)"]
                        GQN_NEW["GoodsQualityNote_GQN_New.aspx<br/>Create GQN"]:::aspnetPage
                        GQN_NEW_DETAILS["GoodsQualityNote_GQN_New_Details.aspx<br/>GQN New Details"]:::aspnetPage
                        GQN_EDIT["GoodsQualityNote_GQN_Edit.aspx<br/>Edit GQN"]:::aspnetPage
                        GQN_EDIT_DETAILS["GoodsQualityNote_GQN_Edit_Details.aspx<br/>GQN Edit Details"]:::aspnetPage
                        GQN_DELETE["GoodsQualityNote_GQN_Delete.aspx<br/>Delete GQN"]:::aspnetPage
                        GQN_DELETE_DETAILS["GoodsQualityNote_GQN_Delete_Details.aspx<br/>GQN Delete Details"]:::aspnetPage
                        GQN_PRINT["GoodsQualityNote_GQN_Print.aspx<br/>Print GQN"]:::aspnetPage
                        GQN_PRINT_DETAILS["GoodsQualityNote_GQN_Print_Details.aspx<br/>GQN Print Details"]:::aspnetPage
                    end
                    
                    %% Material Return Quality Note (MRQN)
                    subgraph MRQN_SECTION["📋 Material Return Quality Note (MRQN)"]
                        MRN_DASH["MaterialReturnNote_Dashboard.aspx<br/>Material Return Dashboard"]:::aspnetPage
                        MRQN_NEW["MaterialReturnQualityNote_MRQN_New.aspx<br/>Create MRQN"]:::aspnetPage
                        MRQN_NEW_DETAILS["MaterialReturnQualityNote_MRQN_New_Details.aspx<br/>MRQN New Details"]:::aspnetPage
                        MRQN_EDIT["MaterialReturnQualityNote_MRQN_Edit.aspx<br/>Edit MRQN"]:::aspnetPage
                        MRQN_EDIT_DETAILS["MaterialReturnQualityNote_MRQN_Edit_Details.aspx<br/>MRQN Edit Details"]:::aspnetPage
                        MRQN_DELETE["MaterialReturnQualityNote_MRQN_Delete.aspx<br/>Delete MRQN"]:::aspnetPage
                        MRQN_DELETE_DETAILS["MaterialReturnQualityNote_MRQN_Delete_Details.aspx<br/>MRQN Delete Details"]:::aspnetPage
                        MRQN_PRINT["MaterialReturnQualityNote_MRQN_Print.aspx<br/>Print MRQN"]:::aspnetPage
                        MRQN_PRINT_DETAILS["MaterialReturnQualityNote_MRQN_Print_Details.aspx<br/>MRQN Print Details"]:::aspnetPage
                    end
                end
                
                %% Goods Rejection Management
                subgraph GOODS_REJECTION["❌ Goods Rejection Management"]
                    GOODS_REJECTION_GRN["GoodsRejection_GRN.aspx<br/>Goods Rejection Note"]:::aspnetPage
                    GOODS_REJECTION_PRINT["GoodsRejection_GRN_Print_Details.aspx<br/>Rejection Print Details"]:::aspnetPage
                end
                
                %% Authorized MCN
                subgraph AUTHORIZED_MCN["✅ Authorized MCN"]
                    AUTH_MCN["AuthorizedMCN.aspx<br/>Authorized Material Credit Note"]:::aspnetPage
                    AUTH_MCN_DETAILS["AuthorizedMCN_Details.aspx<br/>Authorized MCN Details"]:::aspnetPage
                end
                
                %% Inspection Management
                subgraph INSPECTION_MGMT["🔍 Inspection Management"]
                    INCOMING_INSPECTION["Incoming_Inspection.aspx<br/>Incoming Inspection"]:::aspnetPage
                    IN_PROCESS_INSPECTION["InProcess_Inspection.aspx<br/>In-Process Inspection"]:::aspnetPage
                    FINAL_INSPECTION["Final_Inspection.aspx<br/>Final Inspection"]:::aspnetPage
                    RANDOM_INSPECTION["Random_Inspection.aspx<br/>Random Inspection"]:::aspnetPage
                    BATCH_INSPECTION["Batch_Inspection.aspx<br/>Batch Inspection"]:::aspnetPage
                    LOT_INSPECTION["Lot_Inspection.aspx<br/>Lot Inspection"]:::aspnetPage
                end
                
                %% Quality Testing
                subgraph QUALITY_TESTING["🧪 Quality Testing"]
                    MATERIAL_TESTING["Material_Testing.aspx<br/>Material Testing"]:::aspnetPage
                    DIMENSIONAL_TESTING["Dimensional_Testing.aspx<br/>Dimensional Testing"]:::aspnetPage
                    FUNCTIONAL_TESTING["Functional_Testing.aspx<br/>Functional Testing"]:::aspnetPage
                    PERFORMANCE_TESTING["Performance_Testing.aspx<br/>Performance Testing"]:::aspnetPage
                    DESTRUCTIVE_TESTING["Destructive_Testing.aspx<br/>Destructive Testing"]:::aspnetPage
                    NON_DESTRUCTIVE_TESTING["NonDestructive_Testing.aspx<br/>Non-Destructive Testing"]:::aspnetPage
                end
                
                %% Calibration Management
                subgraph CALIBRATION_MGMT["⚖️ Calibration Management"]
                    INSTRUMENT_CALIBRATION["Instrument_Calibration.aspx<br/>Instrument Calibration"]:::aspnetPage
                    CALIBRATION_CERTIFICATE["Calibration_Certificate.aspx<br/>Calibration Certificate"]:::aspnetPage
                    CALIBRATION_SCHEDULE_MGMT["Calibration_Schedule_Management.aspx<br/>Calibration Schedule Management"]:::aspnetPage
                    CALIBRATION_TRACKING["Calibration_Tracking.aspx<br/>Calibration Tracking"]:::aspnetPage
                end
                
                %% Non-Conformance Management
                subgraph NCR_MGMT["⚠️ Non-Conformance Management"]
                    NCR_CREATION["NCR_Creation.aspx<br/>NCR Creation"]:::aspnetPage
                    NCR_INVESTIGATION["NCR_Investigation.aspx<br/>NCR Investigation"]:::aspnetPage
                    CORRECTIVE_ACTION["Corrective_Action.aspx<br/>Corrective Action"]:::aspnetPage
                    PREVENTIVE_ACTION["Preventive_Action.aspx<br/>Preventive Action"]:::aspnetPage
                    NCR_CLOSURE["NCR_Closure.aspx<br/>NCR Closure"]:::aspnetPage
                end
                
                %% Supplier Quality Management
                subgraph SUPPLIER_QUALITY["🏭 Supplier Quality Management"]
                    SUPPLIER_AUDIT["Supplier_Audit.aspx<br/>Supplier Audit"]:::aspnetPage
                    SUPPLIER_RATING["Supplier_Rating.aspx<br/>Supplier Rating"]:::aspnetPage
                    SUPPLIER_DEVELOPMENT["Supplier_Development.aspx<br/>Supplier Development"]:::aspnetPage
                    VENDOR_ASSESSMENT["Vendor_Assessment.aspx<br/>Vendor Assessment"]:::aspnetPage
                end
            end

            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage

                %% Quality Reports
                subgraph QUALITY_REPORTS["📈 Quality Reports"]
                    GOODS_REJECTION_REPORT["GoodsRejection_GRN.aspx<br/>Goods Rejection Report"]:::aspnetPage
                    GOODS_REJECTION_PRINT_REP["GoodsRejection_GRN_Print_Details.aspx<br/>Rejection Print Report"]:::aspnetPage
                    SCRAP_MATERIAL_REPORT["ScrapMaterial_Report.aspx<br/>Scrap Material Report"]:::aspnetPage
                    SCRAP_MATERIAL_DETAILS["ScrapMaterial_Report_Details.aspx<br/>Scrap Material Details"]:::aspnetPage
                    QUALITY_SUMMARY_REPORT["Quality_Summary_Report.aspx<br/>Quality Summary"]:::aspnetPage
                    INSPECTION_SUMMARY["Inspection_Summary.aspx<br/>Inspection Summary"]:::aspnetPage
                end

                %% Inspection Reports
                subgraph INSPECTION_REPORTS["🔍 Inspection Reports"]
                    INCOMING_INSPECTION_REPORT["Incoming_Inspection_Report.aspx<br/>Incoming Inspection Report"]:::aspnetPage
                    INPROCESS_INSPECTION_REPORT["InProcess_Inspection_Report.aspx<br/>In-Process Inspection Report"]:::aspnetPage
                    FINAL_INSPECTION_REPORT["Final_Inspection_Report.aspx<br/>Final Inspection Report"]:::aspnetPage
                    INSPECTION_STATUS_REPORT["Inspection_Status_Report.aspx<br/>Inspection Status Report"]:::aspnetPage
                    DEFECT_ANALYSIS_REPORT["Defect_Analysis_Report.aspx<br/>Defect Analysis Report"]:::aspnetPage
                end

                %% Supplier Quality Reports
                subgraph SUPPLIER_QC_REPORTS["🏭 Supplier Quality Reports"]
                    SUPPLIER_QUALITY_REPORT["Supplier_Quality_Report.aspx<br/>Supplier Quality Report"]:::aspnetPage
                    VENDOR_RATING_REPORT["Vendor_Rating_Report.aspx<br/>Vendor Rating Report"]:::aspnetPage
                    SUPPLIER_PERFORMANCE_QC["Supplier_Performance_QC.aspx<br/>Supplier Performance QC"]:::aspnetPage
                    SUPPLIER_AUDIT_REPORT["Supplier_Audit_Report.aspx<br/>Supplier Audit Report"]:::aspnetPage
                end

                %% NCR Reports
                subgraph NCR_REPORTS["⚠️ NCR Reports"]
                    NCR_SUMMARY_REPORT["NCR_Summary_Report.aspx<br/>NCR Summary Report"]:::aspnetPage
                    CORRECTIVE_ACTION_REPORT["Corrective_Action_Report.aspx<br/>Corrective Action Report"]:::aspnetPage
                    PREVENTIVE_ACTION_REPORT["Preventive_Action_Report.aspx<br/>Preventive Action Report"]:::aspnetPage
                    NCR_TREND_ANALYSIS["NCR_Trend_Analysis.aspx<br/>NCR Trend Analysis"]:::aspnetPage
                end

                %% Calibration Reports
                subgraph CALIBRATION_REPORTS["⚖️ Calibration Reports"]
                    CALIBRATION_STATUS_REPORT["Calibration_Status_Report.aspx<br/>Calibration Status Report"]:::aspnetPage
                    CALIBRATION_DUE_REPORT["Calibration_Due_Report.aspx<br/>Calibration Due Report"]:::aspnetPage
                    INSTRUMENT_HISTORY_REPORT["Instrument_History_Report.aspx<br/>Instrument History Report"]:::aspnetPage
                    CALIBRATION_CERTIFICATE_REPORT["Calibration_Certificate_Report.aspx<br/>Calibration Certificate Report"]:::aspnetPage
                end

                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    GRN_RPT["GRN.rpt<br/>Goods Rejection Note Report"]:::report
                    SCRAP_REPORT_RPT["ScrapReport.rpt<br/>Scrap Report"]:::report
                    GQN_RPT["GQN.rpt<br/>Goods Quality Note Report"]:::report
                    MRQN_RPT["MRQN.rpt<br/>Material Return Quality Note Report"]:::report
                    INSPECTION_RPT["Inspection.rpt<br/>Inspection Report"]:::report
                    NCR_RPT["NCR.rpt<br/>Non-Conformance Report"]:::report
                    CALIBRATION_RPT["Calibration.rpt<br/>Calibration Report"]:::report
                    SUPPLIER_AUDIT_RPT["Supplier_Audit.rpt<br/>Supplier Audit Report"]:::report
                end
            end
        end
