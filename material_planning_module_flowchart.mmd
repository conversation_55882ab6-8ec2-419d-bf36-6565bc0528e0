graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Material Planning Module Structure
    subgraph MP_MODULE["📋 Material Planning Module - MRP & Production Planning System"]
        direction TB
        
        %% Entry Points
        MP_DASHBOARD[("📊 Material Planning Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Material Planning Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Item Process Management
                subgraph ITEM_PROCESS["🔧 Item Process Management"]
                    ITEM_PROCESS["ItemProcess.aspx<br/>Item Process Master"]:::aspnetPage
                    BOM_MASTER["BOM_Master.aspx<br/>Bill of Materials Master"]:::aspnetPage
                    ROUTING_MASTER["Routing_Master.aspx<br/>Routing Master"]:::aspnetPage
                    WORK_CENTER_MASTER["WorkCenter_Master.aspx<br/>Work Center Master"]:::aspnetPage
                    OPERATION_MASTER["Operation_Master.aspx<br/>Operation Master"]:::aspnetPage
                    RESOURCE_MASTER["Resource_Master.aspx<br/>Resource Master"]:::aspnetPage
                end
                
                %% Planning Parameters
                subgraph PLANNING_PARAMS["📊 Planning Parameters"]
                    PLANNING_HORIZON["Planning_Horizon.aspx<br/>Planning Horizon"]:::aspnetPage
                    LEAD_TIME_MASTER["LeadTime_Master.aspx<br/>Lead Time Master"]:::aspnetPage
                    SAFETY_STOCK["Safety_Stock.aspx<br/>Safety Stock"]:::aspnetPage
                    REORDER_LEVEL["Reorder_Level.aspx<br/>Reorder Level"]:::aspnetPage
                    LOT_SIZE_RULES["LotSize_Rules.aspx<br/>Lot Size Rules"]:::aspnetPage
                    PLANNING_POLICY["Planning_Policy.aspx<br/>Planning Policy"]:::aspnetPage
                end
                
                %% Demand Management
                subgraph DEMAND_MGMT["📈 Demand Management"]
                    DEMAND_FORECAST["Demand_Forecast.aspx<br/>Demand Forecast"]:::aspnetPage
                    SALES_FORECAST["Sales_Forecast.aspx<br/>Sales Forecast"]:::aspnetPage
                    INDEPENDENT_DEMAND["Independent_Demand.aspx<br/>Independent Demand"]:::aspnetPage
                    DEPENDENT_DEMAND["Dependent_Demand.aspx<br/>Dependent Demand"]:::aspnetPage
                    DEMAND_PATTERN["Demand_Pattern.aspx<br/>Demand Pattern"]:::aspnetPage
                end
                
                %% Capacity Planning
                subgraph CAPACITY_PLANNING["⚙️ Capacity Planning"]
                    CAPACITY_MASTER["Capacity_Master.aspx<br/>Capacity Master"]:::aspnetPage
                    WORK_CENTER_CAPACITY["WorkCenter_Capacity.aspx<br/>Work Center Capacity"]:::aspnetPage
                    RESOURCE_CAPACITY["Resource_Capacity.aspx<br/>Resource Capacity"]:::aspnetPage
                    SHIFT_CALENDAR["Shift_Calendar.aspx<br/>Shift Calendar"]:::aspnetPage
                    HOLIDAY_CALENDAR["Holiday_Calendar.aspx<br/>Holiday Calendar"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: MaterialPlanning_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Material Planning
                subgraph MATERIAL_PLANNING["📋 Material Planning"]
                    PLANNING_NEW["Planning_New.aspx<br/>Create Material Plan"]:::aspnetPage
                    PLANNING_EDIT["Planning_Edit.aspx<br/>Edit Material Plan"]:::aspnetPage
                    PLANNING_EDIT_DETAILS["Planning_Edit_Details.aspx<br/>Planning Edit Details"]:::aspnetPage
                    PLANNING_DELETE["Planning_Delete.aspx<br/>Delete Material Plan"]:::aspnetPage
                    PLANNING_DELETE_PLAN["Planning_Delete_Plan.aspx<br/>Delete Plan Details"]:::aspnetPage
                    PLANNING_PRINT["Planning_Print.aspx<br/>Print Material Plan"]:::aspnetPage
                    PLANNING_PRINT_DETAILS["Planning_Print_Details.aspx<br/>Planning Print Details"]:::aspnetPage
                    PDT["pdt.aspx<br/>Production Data Transfer"]:::aspnetPage
                end
                
                %% MRP Processing
                subgraph MRP_PROCESSING["🔄 MRP Processing"]
                    MRP_RUN["MRP_Run.aspx<br/>MRP Run"]:::aspnetPage
                    MRP_REGENERATION["MRP_Regeneration.aspx<br/>MRP Regeneration"]:::aspnetPage
                    MRP_NET_CHANGE["MRP_NetChange.aspx<br/>MRP Net Change"]:::aspnetPage
                    MRP_EXPLOSION["MRP_Explosion.aspx<br/>MRP Explosion"]:::aspnetPage
                    MRP_CALCULATION["MRP_Calculation.aspx<br/>MRP Calculation"]:::aspnetPage
                    MRP_VALIDATION["MRP_Validation.aspx<br/>MRP Validation"]:::aspnetPage
                end
                
                %% Production Planning
                subgraph PRODUCTION_PLANNING["🏭 Production Planning"]
                    PRODUCTION_PLAN_NEW["ProductionPlan_New.aspx<br/>Create Production Plan"]:::aspnetPage
                    PRODUCTION_PLAN_EDIT["ProductionPlan_Edit.aspx<br/>Edit Production Plan"]:::aspnetPage
                    PRODUCTION_PLAN_DELETE["ProductionPlan_Delete.aspx<br/>Delete Production Plan"]:::aspnetPage
                    PRODUCTION_SCHEDULE["Production_Schedule.aspx<br/>Production Schedule"]:::aspnetPage
                    CAPACITY_PLANNING_TRANS["Capacity_Planning.aspx<br/>Capacity Planning"]:::aspnetPage
                    RESOURCE_PLANNING["Resource_Planning.aspx<br/>Resource Planning"]:::aspnetPage
                end
                
                %% Purchase Planning
                subgraph PURCHASE_PLANNING["🛒 Purchase Planning"]
                    PURCHASE_PLAN_NEW["PurchasePlan_New.aspx<br/>Create Purchase Plan"]:::aspnetPage
                    PURCHASE_PLAN_EDIT["PurchasePlan_Edit.aspx<br/>Edit Purchase Plan"]:::aspnetPage
                    PURCHASE_PLAN_DELETE["PurchasePlan_Delete.aspx<br/>Delete Purchase Plan"]:::aspnetPage
                    PURCHASE_REQUISITION_PLAN["PurchaseRequisition_Plan.aspx<br/>Purchase Requisition Planning"]:::aspnetPage
                    SUPPLIER_PLANNING["Supplier_Planning.aspx<br/>Supplier Planning"]:::aspnetPage
                    DELIVERY_SCHEDULE_PLAN["DeliverySchedule_Plan.aspx<br/>Delivery Schedule Planning"]:::aspnetPage
                end
                
                %% Inventory Planning
                subgraph INVENTORY_PLANNING["📦 Inventory Planning"]
                    INVENTORY_PLAN_NEW["InventoryPlan_New.aspx<br/>Create Inventory Plan"]:::aspnetPage
                    INVENTORY_PLAN_EDIT["InventoryPlan_Edit.aspx<br/>Edit Inventory Plan"]:::aspnetPage
                    INVENTORY_PLAN_DELETE["InventoryPlan_Delete.aspx<br/>Delete Inventory Plan"]:::aspnetPage
                    STOCK_LEVEL_PLANNING["StockLevel_Planning.aspx<br/>Stock Level Planning"]:::aspnetPage
                    REORDER_PLANNING["Reorder_Planning.aspx<br/>Reorder Planning"]:::aspnetPage
                    ABC_PLANNING["ABC_Planning.aspx<br/>ABC Analysis Planning"]:::aspnetPage
                end
                
                %% Demand Planning
                subgraph DEMAND_PLANNING["📈 Demand Planning"]
                    DEMAND_PLAN_NEW["DemandPlan_New.aspx<br/>Create Demand Plan"]:::aspnetPage
                    DEMAND_PLAN_EDIT["DemandPlan_Edit.aspx<br/>Edit Demand Plan"]:::aspnetPage
                    DEMAND_PLAN_DELETE["DemandPlan_Delete.aspx<br/>Delete Demand Plan"]:::aspnetPage
                    FORECAST_PLANNING["Forecast_Planning.aspx<br/>Forecast Planning"]:::aspnetPage
                    SEASONAL_PLANNING["Seasonal_Planning.aspx<br/>Seasonal Planning"]:::aspnetPage
                    TREND_ANALYSIS_PLAN["TrendAnalysis_Plan.aspx<br/>Trend Analysis Planning"]:::aspnetPage
                end
                
                %% Schedule Management
                subgraph SCHEDULE_MGMT["📅 Schedule Management"]
                    MASTER_SCHEDULE["Master_Schedule.aspx<br/>Master Production Schedule"]:::aspnetPage
                    DETAILED_SCHEDULE["Detailed_Schedule.aspx<br/>Detailed Schedule"]:::aspnetPage
                    SCHEDULE_OPTIMIZATION["Schedule_Optimization.aspx<br/>Schedule Optimization"]:::aspnetPage
                    SCHEDULE_VALIDATION["Schedule_Validation.aspx<br/>Schedule Validation"]:::aspnetPage
                    SCHEDULE_RELEASE["Schedule_Release.aspx<br/>Schedule Release"]:::aspnetPage
                end
                
                %% Exception Management
                subgraph EXCEPTION_MGMT["⚠️ Exception Management"]
                    EXCEPTION_REPORT["Exception_Report.aspx<br/>Exception Report"]:::aspnetPage
                    SHORTAGE_ANALYSIS["Shortage_Analysis.aspx<br/>Shortage Analysis"]:::aspnetPage
                    OVERSTOCK_ANALYSIS["Overstock_Analysis.aspx<br/>Overstock Analysis"]:::aspnetPage
                    CAPACITY_EXCEPTION["Capacity_Exception.aspx<br/>Capacity Exception"]:::aspnetPage
                    LEAD_TIME_EXCEPTION["LeadTime_Exception.aspx<br/>Lead Time Exception"]:::aspnetPage
                end
            end
