graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Design Module Structure
    subgraph DESIGN_MODULE["🎨 Design Module - Product Design & Engineering System"]
        direction TB
        
        %% Entry Points
        DESIGN_DASHBOARD[("📊 Design Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Design Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Item Master Management
                subgraph ITEM_MASTER_MGMT["📦 Item Master Management"]
                    ITEM_MASTER_NEW["ItemMaster_New.aspx<br/>Create Item Master"]:::aspnetPage
                    ITEM_MASTER_EDIT["ItemMaster_Edit.aspx<br/>Edit Item Master"]:::aspnetPage
                    ITEM_MASTER_EDIT_DETAILS["ItemMaster_Edit_Details.aspx<br/>Item Master Edit Details"]:::aspnetPage
                    ITEM_MASTER_DELETE["ItemMaster_Delete.aspx<br/>Delete Item Master"]:::aspnetPage
                    ITEM_MASTER_PRINT["ItemMaster_Print.aspx<br/>Print Item Master"]:::aspnetPage
                    ITEM_MASTER_PRINT_DETAILS["ItemMaster_Print_Details.aspx<br/>Item Master Print Details"]:::aspnetPage
                    ITEM_REVISION["ItemRevision.aspx<br/>Item Revision"]:::aspnetPage
                end
                
                %% Category Management
                subgraph CATEGORY_MGMT["📂 Category Management"]
                    CATEGORY["Category.aspx<br/>Category Master"]:::aspnetPage
                    CATEGORY_NEW["CategoryNew.aspx<br/>Create Category"]:::aspnetPage
                    CATEGORY_EDIT["CategoryEdit.aspx<br/>Edit Category"]:::aspnetPage
                    CATEGORY_DELETE["CategoryDelete.aspx<br/>Delete Category"]:::aspnetPage
                    
                    SUB_CATEGORY["SubCategory.aspx<br/>Sub Category Master"]:::aspnetPage
                    SUB_CATEGORY_NEW["SubCategoryNew.aspx<br/>Create Sub Category"]:::aspnetPage
                    SUB_CATEGORY_EDIT["SubCategoryEdit.aspx<br/>Edit Sub Category"]:::aspnetPage
                    SUB_CATEGORY_DELETE["SubCategoryDelete.aspx<br/>Delete Sub Category"]:::aspnetPage
                end
                
                %% Unit & ECN Management
                subgraph UNIT_ECN_MGMT["⚙️ Unit & ECN Management"]
                    UNIT_MASTER["Unit_Master.aspx<br/>Unit Master"]:::aspnetPage
                    ECN_REASON_TYPES["ECNReasonTypes.aspx<br/>ECN Reason Types"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: Design_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                TRANS_BOM_DASH["Dashboard_BOM.aspx<br/>BOM Dashboard"]:::aspnetPage
                TRANS_SLIDO_DASH["Dashboard_Slido.aspx<br/>Slido Dashboard"]:::aspnetPage
                
                %% BOM Design Management
                subgraph BOM_DESIGN["🔧 BOM Design Management"]
                    %% BOM Assembly Operations
                    BOM_ASSEMBLY_NEW["BOM_Design_Assembly_New.aspx<br/>Create BOM Assembly"]:::aspnetPage
                    BOM_ASSEMBLY_EDIT["BOM_Design_Assembly_Edit.aspx<br/>Edit BOM Assembly"]:::aspnetPage
                    BOM_ITEM_EDIT["BOM_Design_Item_Edit.aspx<br/>Edit BOM Item"]:::aspnetPage
                    BOM_DELETE["BOM_Design_Delete.aspx<br/>Delete BOM"]:::aspnetPage
                    
                    %% BOM Work Order Operations
                    BOM_WO_GRID["BOM_Design_WO_Grid.aspx<br/>BOM Work Order Grid"]:::aspnetPage
                    BOM_WO_GRID_UPDATE["BOM_Design_WO_Grid_Update.aspx<br/>Update BOM WO Grid"]:::aspnetPage
                    BOM_WO_TREEVIEW["BOM_Design_WO_TreeView.aspx<br/>BOM Work Order TreeView"]:::aspnetPage
                    BOM_WO_TREEVIEW_EDIT["BOM_Design_WO_TreeView_Edit.aspx<br/>Edit BOM WO TreeView"]:::aspnetPage
                    BOM_WO_TREEVIEW_DELETE["BOM_Design_WO_TreeView_Delete.aspx<br/>Delete BOM WO TreeView"]:::aspnetPage
                    
                    %% BOM Copy Operations
                    BOM_COPY_WO["BOM_Design_CopyWo.aspx<br/>Copy BOM Work Order"]:::aspnetPage
                    BOM_COPY_TREE["BOM_Design_Copy_Tree.aspx<br/>Copy BOM Tree"]:::aspnetPage
                    BOM_ROOT_ASSEMBLY_COPY_GRID["BOM_Design_Root_Assembly_Copy_Grid.aspx<br/>Root Assembly Copy Grid"]:::aspnetPage
                    BOM_ROOT_ASSEMBLY_COPY_WO["BOM_Design_Root_Assembly_Copy_WO.aspx<br/>Root Assembly Copy WO"]:::aspnetPage
                    
                    %% BOM Print Operations
                    BOM_PRINT_WO["BOM_Design_PrintWo.aspx<br/>Print BOM Work Order"]:::aspnetPage
                    BOM_PRINT_CRY["BOM_Design_Print_Cry.aspx<br/>Print BOM Crystal"]:::aspnetPage
                    BOM_PRINT_TREE["BOM_Design_Print_Tree.aspx<br/>Print BOM Tree"]:::aspnetPage
                    
                    %% BOM Amendment & Upload
                    BOM_AMD["BOM_Amd.aspx<br/>BOM Amendment"]:::aspnetPage
                    BOM_UPLOAD_DRW["BOM_UploadDrw.aspx<br/>Upload BOM Drawing"]:::aspnetPage
                    BOM_WO_ITEMS["BOM_WoItems.aspx<br/>BOM Work Order Items"]:::aspnetPage
                end
                
                %% TPL Design Management
                subgraph TPL_DESIGN["📐 TPL Design Management"]
                    %% TPL Assembly Operations
                    TPL_ASSEMBLY_NEW["TPL_Design_Assembly_New.aspx<br/>Create TPL Assembly"]:::aspnetPage
                    TPL_ASSEMBLY_EDIT["TPL_Design_Assembly_Edit.aspx<br/>Edit TPL Assembly"]:::aspnetPage
                    TPL_NEW["TPL_Design_New.aspx<br/>Create TPL Design"]:::aspnetPage
                    TPL_EDIT["TPL_Design_Edit.aspx<br/>Edit TPL Design"]:::aspnetPage
                    TPL_ITEM_EDIT["TPL_Design_Item_Edit.aspx<br/>Edit TPL Item"]:::aspnetPage
                    TPL_DELETE["TPL_Design_Delete.aspx<br/>Delete TPL"]:::aspnetPage
                    
                    %% TPL Work Order Operations
                    TPL_WO_GRID["TPL_Design_WO_Grid.aspx<br/>TPL Work Order Grid"]:::aspnetPage
                    TPL_WO_GRID_UPDATE["TPL_Design_WO_Grid_Update.aspx<br/>Update TPL WO Grid"]:::aspnetPage
                    TPL_WO_TREEVIEW["TPL_Design_WO_TreeView.aspx<br/>TPL Work Order TreeView"]:::aspnetPage
                    TPL_WO_TREEVIEW_EDIT["TPL_Design_WO_TreeView_Edit.aspx<br/>Edit TPL WO TreeView"]:::aspnetPage
                    TPL_WO_TREEVIEW_DELETE["TPL_Design_WO_TreeView_Delete.aspx<br/>Delete TPL WO TreeView"]:::aspnetPage
                    
                    %% TPL Copy Operations
                    TPL_COPY_WO["TPL_Design_CopyWo.aspx<br/>Copy TPL Work Order"]:::aspnetPage
                    TPL_COPY_TREE["TPL_Design_Copy_Tree.aspx<br/>Copy TPL Tree"]:::aspnetPage
                    TPL_ROOT_ASSEMBLY_COPY_GRID["TPL_Design_Root_Assembly_Copy_Grid.aspx<br/>TPL Root Assembly Copy Grid"]:::aspnetPage
                    TPL_ROOT_ASSEMBLY_COPY_WO["TPL_Design_Root_Assembly_Copy_WO.aspx<br/>TPL Root Assembly Copy WO"]:::aspnetPage
                    
                    %% TPL Print Operations
                    TPL_PRINT_WO["TPL_Design_PrintWo.aspx<br/>Print TPL Work Order"]:::aspnetPage
                    TPL_PRINT_CRY["TPL_Design_Print_Cry.aspx<br/>Print TPL Crystal"]:::aspnetPage
                    TPL_PRINT_TREE["TPL_Design_Print_Tree.aspx<br/>Print TPL Tree"]:::aspnetPage
                    
                    %% TPL Amendment
                    TPL_AMD["TPL_Amd.aspx<br/>TPL Amendment"]:::aspnetPage
                end
                
                %% ECN Management
                subgraph ECN_MGMT["📝 ECN Management"]
                    ECN_MASTER["ECN_Master.aspx<br/>ECN Master"]:::aspnetPage
                    ECN_MASTER_EDIT["ECN_Master_Edit.aspx<br/>Edit ECN Master"]:::aspnetPage
                    ECN_WO["ECN_WO.aspx<br/>ECN Work Order"]:::aspnetPage
                    ECN_UNLOCK["ECNUnlock.aspx<br/>ECN Unlock"]:::aspnetPage
                end
                
                %% Slido Gunrail Management
                subgraph SLIDO_GUNRAIL["🔫 Slido Gunrail Management"]
                    SLIDO_GUNRAIL_DETAILS["Slido_Gunrail_Details.aspx<br/>Slido Gunrail Details"]:::aspnetPage
                    SLIDO_GUNRAIL_WO_GRID["Slido_Gunrail_WO_Grid.aspx<br/>Slido Gunrail WO Grid"]:::aspnetPage
                end
                
                %% Document Management
                subgraph DOCUMENT_MGMT["📄 Document Management"]
                    UPLOAD_DRW["UploadDrw.aspx<br/>Upload Drawing"]:::aspnetPage
                    WO_ITEMS["WoItems.aspx<br/>Work Order Items"]:::aspnetPage
                    IMG["img.aspx<br/>Image Management"]:::aspnetPage
                end
            end
